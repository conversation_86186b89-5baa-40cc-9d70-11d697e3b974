/**
 * @file point_cloud.cpp
 * @brief 点云数据结构实现
 * @details 实现点云数据的存储、操作和变换功能
 */

#include "common/point_cloud.h"
#include "common/transformation_engine.h"
#include <algorithm>
#include <random>
#include <iomanip>

namespace blade_analysis {

// ============================================================================
// 构造函数和析构函数
// ============================================================================

PointCloud::PointCloud() : points_(0, 3) {
}

PointCloud::PointCloud(const PointCloudData& points) : points_(points) {
    BLADE_CHECK_DATA(points.cols() == 3, "点云数据必须是N×3矩阵");
}

PointCloud::PointCloud(const std::vector<Point3D>& points) : points_(points.size(), 3) {
    for (size_t i = 0; i < points.size(); ++i) {
        points_.row(i) = points[i].transpose();
    }
}

PointCloud::PointCloud(const PointCloud& other) : points_(other.points_) {
}

PointCloud::PointCloud(PointCloud&& other) noexcept : points_(std::move(other.points_)) {
}

PointCloud& PointCloud::operator=(const PointCloud& other) {
    if (this != &other) {
        points_ = other.points_;
    }
    return *this;
}

PointCloud& PointCloud::operator=(PointCloud&& other) noexcept {
    if (this != &other) {
        points_ = std::move(other.points_);
    }
    return *this;
}

// ============================================================================
// 数据访问方法
// ============================================================================

Point3D PointCloud::getPoint(size_t index) const {
    validateIndex(index);
    return points_.row(index).transpose();
}

void PointCloud::setPoint(size_t index, const Point3D& point) {
    validateIndex(index);
    points_.row(index) = point.transpose();
}

void PointCloud::clear() {
    points_.resize(0, 3);
}

void PointCloud::resize(size_t new_size) {
    points_.conservativeResize(new_size, 3);
}

// ============================================================================
// 点云操作方法
// ============================================================================

void PointCloud::addPoint(const Point3D& point) {
    size_t old_size = size();
    resize(old_size + 1);
    setPoint(old_size, point);
}

void PointCloud::addPoints(const std::vector<Point3D>& points) {
    if (points.empty()) return;
    
    size_t old_size = size();
    resize(old_size + points.size());
    
    for (size_t i = 0; i < points.size(); ++i) {
        setPoint(old_size + i, points[i]);
    }
}

void PointCloud::addPoints(const PointCloudData& points) {
    BLADE_CHECK_DATA(points.cols() == 3, "添加的点云数据必须是N×3矩阵");
    
    if (points.rows() == 0) return;
    
    size_t old_size = size();
    resize(old_size + points.rows());
    
    points_.bottomRows(points.rows()) = points;
}

void PointCloud::removePoint(size_t index) {
    validateIndex(index);
    
    size_t n = size();
    if (index < n - 1) {
        // 将后面的点向前移动
        points_.block(index, 0, n - index - 1, 3) = 
            points_.block(index + 1, 0, n - index - 1, 3);
    }
    resize(n - 1);
}

void PointCloud::applyTransform(const Transform4D& transform) {
    validateTransform(transform);
    points_ = TransformationEngine::applyTransform(points_, transform);
}

PointCloud PointCloud::transformed(const Transform4D& transform) const {
    PointCloud result(*this);
    result.applyTransform(transform);
    return result;
}

// ============================================================================
// 几何计算方法
// ============================================================================

Point3D PointCloud::computeCentroid() const {
    BLADE_CHECK_DATA(!empty(), "无法计算空点云的质心");
    return points_.colwise().mean().transpose();
}

std::pair<Point3D, Point3D> PointCloud::computeBoundingBox() const {
    BLADE_CHECK_DATA(!empty(), "无法计算空点云的边界框");
    
    Point3D min_point = points_.colwise().minCoeff().transpose();
    Point3D max_point = points_.colwise().maxCoeff().transpose();
    
    return std::make_pair(min_point, max_point);
}

double PointCloud::computeMaxExtent() const {
    auto [min_point, max_point] = computeBoundingBox();
    return (max_point - min_point).maxCoeff();
}

void PointCloud::centerToOrigin() {
    if (empty()) return;
    
    Point3D centroid = computeCentroid();
    points_.rowwise() -= centroid.transpose();
}

PointCloud PointCloud::centered() const {
    PointCloud result(*this);
    result.centerToOrigin();
    return result;
}

// ============================================================================
// 切片操作方法
// ============================================================================

SliceData2D PointCloud::sliceByY(double y_value, double thickness) const {
    BLADE_CHECK_DATA(thickness > 0, "切片厚度必须大于0");
    
    double half_thickness = thickness / 2.0;
    double y_min = y_value - half_thickness;
    double y_max = y_value + half_thickness;
    
    // 找到Y坐标在范围内的点
    std::vector<int> indices;
    for (int i = 0; i < points_.rows(); ++i) {
        double y = points_(i, 1);
        if (y >= y_min && y <= y_max) {
            indices.push_back(i);
        }
    }
    
    // 提取XZ坐标
    SliceData2D slice(y_value);
    if (!indices.empty()) {
        slice.points.resize(indices.size(), 2);
        for (size_t i = 0; i < indices.size(); ++i) {
            int idx = indices[i];
            slice.points(i, 0) = points_(idx, 0);  // X坐标
            slice.points(i, 1) = points_(idx, 2);  // Z坐标
        }
    }
    
    return slice;
}

std::vector<SliceData2D> PointCloud::sliceByY(const std::vector<double>& y_values, double thickness) const {
    std::vector<SliceData2D> slices;
    slices.reserve(y_values.size());
    
    for (double y : y_values) {
        SliceData2D slice = sliceByY(y, thickness);
        if (!slice.empty()) {
            slices.push_back(std::move(slice));
        }
    }
    
    return slices;
}

// ============================================================================
// 数据验证和统计方法
// ============================================================================

bool PointCloud::isValid() const {
    return points_.cols() == 3 && points_.rows() >= 0 && 
           points_.allFinite();
}

PointCloud::Statistics PointCloud::getStatistics() const {
    Statistics stats;
    
    if (empty()) {
        stats.point_count = 0;
        stats.max_extent = 0.0;
        return stats;
    }
    
    auto [min_point, max_point] = computeBoundingBox();
    stats.min_point = min_point;
    stats.max_point = max_point;
    stats.centroid = computeCentroid();
    stats.max_extent = computeMaxExtent();
    stats.point_count = size();
    
    return stats;
}

void PointCloud::printInfo(const std::string& title) const {
    std::cout << "\n=== " << (title.empty() ? "点云信息" : title) << " ===" << std::endl;
    std::cout << "点数: " << size() << std::endl;
    
    if (!empty()) {
        auto stats = getStatistics();
        std::cout << std::fixed << std::setprecision(4);
        std::cout << "质心: [" << stats.centroid.transpose() << "]" << std::endl;
        std::cout << "最小点: [" << stats.min_point.transpose() << "]" << std::endl;
        std::cout << "最大点: [" << stats.max_point.transpose() << "]" << std::endl;
        std::cout << "最大范围: " << stats.max_extent << std::endl;
    }
    std::cout << std::string(40, '-') << std::endl;
}

// ============================================================================
// 运算符重载
// ============================================================================

bool PointCloud::operator==(const PointCloud& other) const {
    return points_.isApprox(other.points_, constants::NUMERICAL_TOLERANCE);
}

// ============================================================================
// 静态工厂方法
// ============================================================================

PointCloud PointCloud::fromFile(const std::string& filepath) {
    // 这个方法将在DataLoader中实现具体逻辑
    // 这里提供一个前向声明的实现
    throw std::runtime_error("PointCloud::fromFile 需要在DataLoader模块中实现");
}

PointCloud PointCloud::createTestCloud(size_t num_points) {
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_real_distribution<double> dis(-10.0, 10.0);

    PointCloudData points(num_points, 3);
    for (size_t i = 0; i < num_points; ++i) {
        points(i, 0) = dis(gen);
        points(i, 1) = dis(gen);
        points(i, 2) = dis(gen);
    }

    return PointCloud(points);
}

// ============================================================================
// 私有辅助方法
// ============================================================================

void PointCloud::validateIndex(size_t index) const {
    BLADE_CHECK_DATA(index < size(), "点云索引超出范围: " + std::to_string(index));
}

void PointCloud::validateTransform(const Transform4D& transform) const {
    BLADE_CHECK_MATRIX(transform.rows() == 4 && transform.cols() == 4, 
                       "变换矩阵", "必须是4×4矩阵");
}

Point3D PointCloud::fromHomogeneous(const Eigen::Vector4d& homogeneous) {
    return homogeneous.head<3>() / homogeneous(3);
}

Eigen::Vector4d PointCloud::toHomogeneous(const Point3D& point) {
    Eigen::Vector4d result;
    result.head<3>() = point;
    result(3) = 1.0;
    return result;
}

// ============================================================================
// 全局辅助函数
// ============================================================================

std::ostream& operator<<(std::ostream& os, const PointCloud& cloud) {
    os << "PointCloud(size=" << cloud.size() << ")";
    return os;
}

} // namespace blade_analysis
