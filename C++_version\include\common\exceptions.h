/**
 * @file exceptions.h
 * @brief 自定义异常类定义
 * @details 定义项目中使用的各种异常类型，对应Python版本中的异常处理
 * 
 * 对应Python版本：comparison0808.py中的try-except异常处理
 */

#pragma once

#include <stdexcept>
#include <string>
#include <sstream>

namespace blade_analysis {

/**
 * @brief 基础异常类
 * @details 所有自定义异常的基类
 */
class BladeAnalysisException : public std::runtime_error {
public:
    explicit BladeAnalysisException(const std::string& message)
        : std::runtime_error(message) {}
    
    explicit BladeAnalysisException(const char* message)
        : std::runtime_error(message) {}
};

/**
 * @brief 文件操作异常
 * @details 对应Python版本中的文件加载异常
 */
class FileOperationException : public BladeAnalysisException {
public:
    explicit FileOperationException(const std::string& filepath, const std::string& reason)
        : BladeAnalysisException(formatMessage(filepath, reason)), filepath_(filepath) {}
    
    const std::string& getFilePath() const { return filepath_; }

private:
    std::string filepath_;
    
    static std::string formatMessage(const std::string& filepath, const std::string& reason) {
        std::ostringstream oss;
        oss << "文件操作失败: " << filepath << " - " << reason;
        return oss.str();
    }
};

/**
 * @brief 数据验证异常
 * @details 对应Python版本中的数据格式检查异常
 */
class DataValidationException : public BladeAnalysisException {
public:
    explicit DataValidationException(const std::string& message)
        : BladeAnalysisException("数据验证失败: " + message) {}
    
    DataValidationException(const std::string& field, const std::string& expected, const std::string& actual)
        : BladeAnalysisException(formatMessage(field, expected, actual)) {}

private:
    static std::string formatMessage(const std::string& field, const std::string& expected, const std::string& actual) {
        std::ostringstream oss;
        oss << "数据验证失败: " << field << " 期望 " << expected << ", 实际 " << actual;
        return oss.str();
    }
};

/**
 * @brief 矩阵运算异常
 * @details 对应Python版本中的矩阵变换异常
 */
class MatrixOperationException : public BladeAnalysisException {
public:
    explicit MatrixOperationException(const std::string& operation, const std::string& reason)
        : BladeAnalysisException(formatMessage(operation, reason)) {}

private:
    static std::string formatMessage(const std::string& operation, const std::string& reason) {
        std::ostringstream oss;
        oss << "矩阵运算失败: " << operation << " - " << reason;
        return oss.str();
    }
};

/**
 * @brief 配准算法异常
 * @details 对应Python版本中的六点定位和ICP异常
 */
class RegistrationException : public BladeAnalysisException {
public:
    explicit RegistrationException(const std::string& algorithm, const std::string& reason)
        : BladeAnalysisException(formatMessage(algorithm, reason)) {}

private:
    static std::string formatMessage(const std::string& algorithm, const std::string& reason) {
        std::ostringstream oss;
        oss << "配准算法失败: " << algorithm << " - " << reason;
        return oss.str();
    }
};

/**
 * @brief 点云处理异常
 * @details 对应Python版本中的点云操作异常
 */
class PointCloudException : public BladeAnalysisException {
public:
    explicit PointCloudException(const std::string& operation, const std::string& reason)
        : BladeAnalysisException(formatMessage(operation, reason)) {}

private:
    static std::string formatMessage(const std::string& operation, const std::string& reason) {
        std::ostringstream oss;
        oss << "点云处理失败: " << operation << " - " << reason;
        return oss.str();
    }
};

/**
 * @brief 切片分析异常
 * @details 对应Python版本中的切片和误差计算异常
 */
class SliceAnalysisException : public BladeAnalysisException {
public:
    explicit SliceAnalysisException(const std::string& reason)
        : BladeAnalysisException("切片分析失败: " + reason) {}
    
    SliceAnalysisException(double y_value, const std::string& reason)
        : BladeAnalysisException(formatMessage(y_value, reason)) {}

private:
    static std::string formatMessage(double y_value, const std::string& reason) {
        std::ostringstream oss;
        oss << "切片分析失败 (Y=" << y_value << "mm): " << reason;
        return oss.str();
    }
};

/**
 * @brief 可视化异常
 * @details 对应Python版本中的图表生成和PDF报告异常
 */
class VisualizationException : public BladeAnalysisException {
public:
    explicit VisualizationException(const std::string& component, const std::string& reason)
        : BladeAnalysisException(formatMessage(component, reason)) {}

private:
    static std::string formatMessage(const std::string& component, const std::string& reason) {
        std::ostringstream oss;
        oss << "可视化失败: " << component << " - " << reason;
        return oss.str();
    }
};

/**
 * @brief 内存分配异常
 * @details 处理大规模点云数据时的内存问题
 */
class MemoryException : public BladeAnalysisException {
public:
    explicit MemoryException(const std::string& operation, size_t requested_size)
        : BladeAnalysisException(formatMessage(operation, requested_size)) {}

private:
    static std::string formatMessage(const std::string& operation, size_t requested_size) {
        std::ostringstream oss;
        oss << "内存分配失败: " << operation << " 请求大小: " << requested_size << " 字节";
        return oss.str();
    }
};

// ============================================================================
// 异常处理宏定义
// ============================================================================

/// 检查条件，失败时抛出异常
#define BLADE_ASSERT(condition, exception_type, message) \
    do { \
        if (!(condition)) { \
            throw exception_type(message); \
        } \
    } while(0)

/// 检查文件操作结果
#define BLADE_CHECK_FILE(condition, filepath, reason) \
    BLADE_ASSERT(condition, FileOperationException, filepath, reason)

/// 检查数据有效性
#define BLADE_CHECK_DATA(condition, message) \
    BLADE_ASSERT(condition, DataValidationException, message)

/// 检查矩阵操作结果
#define BLADE_CHECK_MATRIX(condition, operation, reason) \
    BLADE_ASSERT(condition, MatrixOperationException, operation, reason)

/// 检查点云操作结果
#define BLADE_CHECK_POINTCLOUD(condition, operation, reason) \
    BLADE_ASSERT(condition, PointCloudException, operation, reason)

} // namespace blade_analysis
