/**
 * @file error_statistics.cpp
 * @brief 误差统计功能实现
 */

#include "common/common_types.h"
#include <algorithm>
#include <numeric>
#include <cmath>

namespace blade_analysis {

// ============================================================================
// ErrorStatistics方法实现
// ============================================================================

void ErrorStatistics::computeStatistics(const DistanceArray& distances) {
    if (distances.empty()) {
        mean_error = 0.0;
        max_error = 0.0;
        std_error = 0.0;
        error_distribution.clear();
        return;
    }
    
    // 复制距离数据
    error_distribution = distances;
    
    // 计算平均误差 (对应Python: np.mean(distances))
    mean_error = std::accumulate(distances.begin(), distances.end(), 0.0) / distances.size();
    
    // 计算最大误差 (对应Python: np.max(distances))
    max_error = *std::max_element(distances.begin(), distances.end());
    
    // 计算标准差 (对应Python: np.std(distances))
    double variance = 0.0;
    for (double dist : distances) {
        double diff = dist - mean_error;
        variance += diff * diff;
    }
    variance /= distances.size();
    std_error = std::sqrt(variance);
}

double ErrorStatistics::getPercentile(double percentile) const {
    if (error_distribution.empty()) {
        return 0.0;
    }
    
    if (percentile <= 0.0) return *std::min_element(error_distribution.begin(), error_distribution.end());
    if (percentile >= 100.0) return *std::max_element(error_distribution.begin(), error_distribution.end());
    
    // 创建排序副本
    std::vector<double> sorted_errors = error_distribution;
    std::sort(sorted_errors.begin(), sorted_errors.end());
    
    // 计算百分位数索引
    double index = (percentile / 100.0) * (sorted_errors.size() - 1);
    size_t lower_index = static_cast<size_t>(std::floor(index));
    size_t upper_index = static_cast<size_t>(std::ceil(index));
    
    if (lower_index == upper_index) {
        return sorted_errors[lower_index];
    }
    
    // 线性插值
    double weight = index - lower_index;
    return sorted_errors[lower_index] * (1.0 - weight) + sorted_errors[upper_index] * weight;
}

} // namespace blade_analysis
