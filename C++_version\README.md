# 叶片分析系统 C++ 版本

## 项目概述

这是基于Python版本 `comparison0808.py` 的C++移植版本，实现了叶片检测和分析的完整流程，包括：

- 六点定位粗配准算法
- ICP精配准算法  
- 切片分析和误差计算
- PDF报告生成

## 项目结构

```
C++_version/
├── CMakeLists.txt              # CMake构建配置
├── README.md                   # 项目说明文档
├── include/                    # 头文件目录
│   ├── common/                 # 通用组件
│   │   ├── common_types.h      # 通用数据类型定义
│   │   ├── exceptions.h        # 自定义异常类
│   │   ├── point_cloud.h       # 点云数据结构
│   │   ├── timer.h             # 高精度计时器
│   │   └── transformation_engine.h # 变换矩阵引擎
│   ├── data/                   # 数据处理模块
│   │   ├── data_loader.h       # 数据加载器
│   │   └── kdtree_wrapper.h    # KDTree封装
│   ├── algorithms/             # 算法模块
│   │   ├── coarse_alignment.h  # 六点定位粗配准
│   │   ├── icp_refinement.h    # ICP精配准
│   │   ├── slice_analyzer.h    # 切片分析器
│   │   └── error_statistics.h  # 误差统计
│   └── visualization/          # 可视化模块
│       ├── visualization_engine.h # 图表生成引擎
│       └── report_generator.h  # PDF报告生成器
├── src/                        # 源文件目录
│   ├── common/                 # 通用组件实现
│   │   ├── point_cloud.cpp
│   │   ├── timer.cpp
│   │   ├── exceptions.cpp
│   │   └── transformation_engine.cpp
│   ├── data/                   # 数据处理实现
│   ├── algorithms/             # 算法实现
│   ├── visualization/          # 可视化实现
│   └── main.cpp                # 主程序入口
├── tests/                      # 单元测试 (待实现)
└── data/                       # 测试数据 (构建时自动复制)
```

## 依赖库

### 必需依赖

1. **Eigen3** (>= 3.3)
   - 高性能线性代数库
   - 用于矩阵运算、SVD分解等
   - 对应Python的numpy

2. **PCL (Point Cloud Library)** (>= 1.8)
   - 专业点云处理库
   - 用于ICP算法、KDTree搜索、体素降采样等
   - 对应Python的open3d

### 可选依赖 (用于可视化)

3. **matplotlib-cpp** 
   - Python matplotlib的C++绑定
   - 用于图表生成

4. **libharu** 或 **Cairo + Pango**
   - PDF生成库
   - 用于报告生成

## 编译说明

### Windows (Visual Studio 2022)

#### 1. 安装依赖库

使用vcpkg包管理器安装依赖：

```bash
# 安装vcpkg (如果尚未安装)
git clone https://github.com/Microsoft/vcpkg.git
cd vcpkg
.\bootstrap-vcpkg.bat
.\vcpkg integrate install

# 安装依赖库
.\vcpkg install eigen3:x64-windows
.\vcpkg install pcl:x64-windows
```

#### 2. 配置CMake

```bash
cd C++_version
mkdir build
cd build

# 配置CMake (使用vcpkg工具链)
cmake .. -DCMAKE_TOOLCHAIN_FILE=[vcpkg根目录]/scripts/buildsystems/vcpkg.cmake -DCMAKE_GENERATOR_PLATFORM=x64
```

#### 3. 编译项目

```bash
# 使用CMake编译
cmake --build . --config Release

# 或者在Visual Studio中打开解决方案文件
start BladeAnalysis.sln
```

### Linux

```bash
# 安装依赖 (Ubuntu/Debian)
sudo apt-get update
sudo apt-get install libeigen3-dev libpcl-dev

# 编译
cd C++_version
mkdir build && cd build
cmake ..
make -j$(nproc)
```

## 运行说明

### 准备测试数据

确保以下数据文件存在于 `data/` 目录中：
- `bladedata.txt` - 叶片点云数据
- `cad_dense.txt` - CAD模型点云数据  
- `大东风二涡六点.txt` - 六点定位参考点

### 运行程序

```bash
# Windows
.\bin\BladeAnalysis.exe

# Linux  
./bin/BladeAnalysis
```

## 开发状态

### 已完成模块 ✅

- [x] 项目结构搭建
- [x] 核心数据类型定义 (`common_types.h`)
- [x] 异常处理系统 (`exceptions.h`)
- [x] 点云数据结构 (`PointCloud`)
- [x] 高精度计时器 (`Timer`)
- [x] 变换矩阵引擎 (`TransformationEngine`)
- [x] CMake构建系统

### 待实现模块 🚧

- [ ] 数据加载模块 (`DataLoader`)
- [ ] KDTree封装 (`KDTreeWrapper`)
- [ ] 六点定位算法 (`CoarseAlignment`)
- [ ] ICP精配准算法 (`ICPRefinement`)
- [ ] 切片分析器 (`SliceAnalyzer`)
- [ ] 误差统计 (`ErrorStatistics`)
- [ ] 可视化引擎 (`VisualizationEngine`)
- [ ] PDF报告生成器 (`ReportGenerator`)
- [ ] 单元测试套件

## 与Python版本对应关系

| Python函数/类 | C++对应组件 | 状态 |
|--------------|-------------|------|
| `load_points()` | `DataLoader::loadPointCloud()` | 待实现 |
| `load_reference_points()` | `DataLoader::loadReferencePoints()` | 待实现 |
| `apply_transform()` | `TransformationEngine::applyTransform()` | ✅ 已完成 |
| `coarse_alignment()` | `CoarseAlignment::performSixPointAlignment()` | 待实现 |
| `icp_refinement()` | `ICPRefinement::performICP()` | 待实现 |
| `slice_by_y()` | `SliceAnalyzer::sliceByY()` | 部分完成 |
| `calculate_2d_errors()` | `ErrorStatistics::calculate2DErrors()` | 待实现 |
| `generate_report()` | `ReportGenerator::generatePDFReport()` | 待实现 |
| `timers` 字典 | `Timer` 类 | ✅ 已完成 |

## 贡献指南

1. 遵循现有的代码风格和命名约定
2. 为新功能添加相应的单元测试
3. 更新文档和注释
4. 确保与Python版本功能对等

## 许可证

本项目采用与原Python版本相同的许可证。
