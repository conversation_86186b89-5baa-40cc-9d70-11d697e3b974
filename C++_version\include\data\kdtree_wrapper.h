/**
 * @file kdtree_wrapper.h
 * @brief KDTree搜索功能封装
 * @details 基于PCL实现的KDTree搜索功能封装
 * 
 * 对应Python版本：comparison0808.py中的KDTree和open3d.geometry.KDTreeFlann
 */

#pragma once

#include "common/common_types.h"
#include "common/exceptions.h"
#include "common/point_cloud.h"
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl/kdtree/kdtree_flann.h>
#include <vector>
#include <memory>

namespace blade_analysis {

/**
 * @brief KDTree搜索结果结构
 */
struct SearchResult {
    std::vector<int> indices;        ///< 搜索到的点索引
    std::vector<double> distances;   ///< 对应的距离
    
    SearchResult() = default;
    SearchResult(const std::vector<int>& idx, const std::vector<double>& dist)
        : indices(idx), distances(dist) {}
    
    /// 获取结果数量
    size_t size() const { return indices.size(); }
    
    /// 检查是否为空
    bool empty() const { return indices.empty(); }
    
    /// 获取最近点索引
    int getNearestIndex() const {
        return empty() ? -1 : indices[0];
    }
    
    /// 获取最近点距离
    double getNearestDistance() const {
        return empty() ? -1.0 : distances[0];
    }
};

/**
 * @brief KDTree封装类
 * @details 提供高效的空间搜索功能，对应Python版本中的KDTree操作
 */
class KDTreeWrapper {
public:
    // ========================================================================
    // 构造函数和析构函数
    // ========================================================================
    
    /// 默认构造函数
    KDTreeWrapper();
    
    /// 从点云构造KDTree
    explicit KDTreeWrapper(const PointCloud& cloud);
    
    /// 从PCL点云构造KDTree
    explicit KDTreeWrapper(const pcl::PointCloud<pcl::PointXYZ>::Ptr& pcl_cloud);
    
    /// 析构函数
    ~KDTreeWrapper() = default;
    
    // 禁用拷贝构造和赋值
    KDTreeWrapper(const KDTreeWrapper&) = delete;
    KDTreeWrapper& operator=(const KDTreeWrapper&) = delete;
    
    // 允许移动构造和赋值
    KDTreeWrapper(KDTreeWrapper&&) = default;
    KDTreeWrapper& operator=(KDTreeWrapper&&) = default;

    // ========================================================================
    // KDTree构建方法
    // ========================================================================
    
    /// 从点云构建KDTree (对应Python: KDTree(points))
    void buildTree(const PointCloud& cloud);
    
    /// 从PCL点云构建KDTree
    void buildTree(const pcl::PointCloud<pcl::PointXYZ>::Ptr& pcl_cloud);
    
    /// 检查KDTree是否已构建
    bool isBuilt() const { return tree_ != nullptr && cloud_ != nullptr; }
    
    /// 获取点云大小
    size_t size() const;
    
    /// 清空KDTree
    void clear();

    // ========================================================================
    // 搜索方法
    // ========================================================================
    
    /// K近邻搜索 (对应Python: tree.query(point, k))
    SearchResult searchKNN(const Point3D& query_point, int k = 1) const;
    
    /// 最近邻搜索 (对应Python: kdtree.search_knn_vector_3d(p, 1))
    int searchNearest(const Point3D& query_point) const;
    
    /// 最近邻搜索，返回距离
    std::pair<int, double> searchNearestWithDistance(const Point3D& query_point) const;
    
    /// 半径搜索
    SearchResult searchRadius(const Point3D& query_point, double radius) const;
    
    /// 批量最近邻搜索
    std::vector<int> searchNearestBatch(const std::vector<Point3D>& query_points) const;
    
    /// 批量K近邻搜索
    std::vector<SearchResult> searchKNNBatch(const std::vector<Point3D>& query_points, int k = 1) const;

    // ========================================================================
    // 距离计算方法
    // ========================================================================
    
    /// 计算点到点云的最近距离 (对应Python: tree.query(blade_slice))
    std::vector<double> computeDistances(const std::vector<Point3D>& query_points) const;
    
    /// 计算点云到点云的最近距离
    std::vector<double> computeDistances(const PointCloud& query_cloud) const;
    
    /// 计算单点到点云的最近距离
    double computeDistance(const Point3D& query_point) const;

    // ========================================================================
    // 统计和分析方法
    // ========================================================================
    
    /// 获取KDTree统计信息
    struct TreeStatistics {
        size_t total_points;
        size_t tree_depth;
        double build_time;
        double average_search_time;
        size_t total_searches;
    };
    TreeStatistics getStatistics() const;
    
    /// 重置搜索统计
    void resetStatistics();
    
    /// 打印KDTree信息
    void printInfo(const std::string& title = "KDTree信息") const;

    // ========================================================================
    // PCL集成方法
    // ========================================================================
    
    /// 获取PCL点云指针
    pcl::PointCloud<pcl::PointXYZ>::Ptr getPCLCloud() const { return cloud_; }
    
    /// 获取PCL KDTree指针
    pcl::KdTreeFLANN<pcl::PointXYZ>::Ptr getPCLTree() const { return tree_; }
    
    /// 从PointCloud转换为PCL点云
    static pcl::PointCloud<pcl::PointXYZ>::Ptr toPCLCloud(const PointCloud& cloud);
    
    /// 从PCL点云转换为PointCloud
    static PointCloud fromPCLCloud(const pcl::PointCloud<pcl::PointXYZ>::Ptr& pcl_cloud);

    // ========================================================================
    // 便利方法
    // ========================================================================
    
    /// 查找六点定位的最近点 (对应Python: coarse_alignment中的KDTree搜索)
    std::vector<Point3D> findNearestPoints(const std::vector<Point3D>& source_points) const;
    
    /// 验证搜索结果
    bool validateSearchResult(const SearchResult& result) const;

private:
    // ========================================================================
    // 私有成员变量
    // ========================================================================
    
    /// PCL KDTree对象
    pcl::KdTreeFLANN<pcl::PointXYZ>::Ptr tree_;
    
    /// PCL点云对象
    pcl::PointCloud<pcl::PointXYZ>::Ptr cloud_;
    
    /// 统计信息
    mutable TreeStatistics stats_;
    
    /// 构建时间记录
    mutable std::chrono::high_resolution_clock::time_point build_start_time_;

    // ========================================================================
    // 私有辅助方法
    // ========================================================================
    
    /// 验证KDTree状态
    void validateTree() const;
    
    /// 验证查询点
    void validateQueryPoint(const Point3D& point) const;
    
    /// 记录搜索时间
    void recordSearchTime(double time) const;
    
    /// 转换PCL搜索结果
    SearchResult convertPCLResult(const std::vector<int>& indices, 
                                  const std::vector<float>& distances) const;
    
    /// Point3D转换为PCL点
    static pcl::PointXYZ toPCLPoint(const Point3D& point);
    
    /// PCL点转换为Point3D
    static Point3D fromPCLPoint(const pcl::PointXYZ& pcl_point);
};

// ============================================================================
// 便利函数
// ============================================================================

/// 快速创建KDTree并搜索最近邻
std::vector<int> quickSearchNearest(const PointCloud& target_cloud, 
                                    const std::vector<Point3D>& query_points);

/// 快速计算点云间距离
std::vector<double> quickComputeDistances(const PointCloud& target_cloud, 
                                          const PointCloud& query_cloud);

} // namespace blade_analysis
