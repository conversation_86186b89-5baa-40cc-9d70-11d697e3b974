/**
 * @file data_loader.cpp
 * @brief 数据加载器类实现
 */

#include "data/data_loader.h"
#include <fstream>
#include <sstream>
#include <iostream>
#include <algorithm>
#include <cctype>
#include <filesystem>

namespace blade_analysis {

// 静态成员变量初始化
bool DataLoader::verbose_ = true;

// ============================================================================
// 点云数据加载方法
// ============================================================================

PointCloud DataLoader::loadPointCloud(const std::string& filepath) {
    logVerbose("\n[加载] " + filepath);
    
    if (!validateFile(filepath)) {
        throw FileOperationException(filepath, "文件不存在或不可读");
    }
    
    try {
        return loadPointCloudAuto(filepath);
    } catch (const std::exception& e) {
        logError("加载失败: " + std::string(e.what()));
        throw FileOperationException(filepath, e.what());
    }
}

PointCloud DataLoader::loadPointCloudFromCSV(const std::string& filepath, char delimiter) {
    std::vector<std::string> lines = readAllLines(filepath);
    std::vector<Point3D> points;
    points.reserve(lines.size());
    
    size_t line_number = 0;
    for (const auto& line : lines) {
        line_number++;
        
        if (shouldSkipLine(line)) {
            continue;
        }
        
        try {
            std::vector<std::string> tokens = parseCSVLine(line, delimiter);
            
            if (tokens.size() < 3) {
                logVerbose("跳过第" + std::to_string(line_number) + "行: 列数不足");
                continue;
            }
            
            Point3D point;
            point.x() = stringToDouble(tokens[0]);
            point.y() = stringToDouble(tokens[1]);
            point.z() = stringToDouble(tokens[2]);
            
            if (isValidPoint(point)) {
                points.push_back(point);
            } else {
                logVerbose("跳过第" + std::to_string(line_number) + "行: 无效坐标");
            }
            
        } catch (const std::exception& e) {
            logVerbose("跳过第" + std::to_string(line_number) + "行: " + e.what());
            continue;
        }
    }
    
    if (points.empty()) {
        throw DataValidationException("未找到有效的点云数据");
    }
    
    PointCloud cloud(points);
    printLoadInfo(filepath, cloud.size());
    return cloud;
}

PointCloud DataLoader::loadPointCloudFromTXT(const std::string& filepath) {
    // TXT文件通常使用空格或制表符分隔
    return loadPointCloudFromCSV(filepath, ' ');
}

PointCloud DataLoader::loadPointCloudFromPLY(const std::string& filepath) {
    // 简化的PLY加载实现，仅支持ASCII格式
    std::ifstream file(filepath);
    if (!file.is_open()) {
        throw FileOperationException(filepath, "无法打开文件");
    }
    
    std::string line;
    bool in_header = true;
    size_t vertex_count = 0;
    std::vector<Point3D> points;
    
    while (std::getline(file, line)) {
        line = trim(line);
        
        if (in_header) {
            if (line.find("element vertex") != std::string::npos) {
                std::istringstream iss(line);
                std::string element, vertex;
                iss >> element >> vertex >> vertex_count;
                points.reserve(vertex_count);
            } else if (line == "end_header") {
                in_header = false;
            }
        } else {
            // 读取顶点数据
            if (points.size() >= vertex_count) break;
            
            std::istringstream iss(line);
            Point3D point;
            if (iss >> point.x() >> point.y() >> point.z()) {
                if (isValidPoint(point)) {
                    points.push_back(point);
                }
            }
        }
    }
    
    if (points.empty()) {
        throw DataValidationException("PLY文件中未找到有效的顶点数据");
    }
    
    PointCloud cloud(points);
    printLoadInfo(filepath, cloud.size());
    return cloud;
}

// ============================================================================
// 参考点加载方法
// ============================================================================

ReferencePointsMap DataLoader::loadReferencePoints(const std::string& filepath) {
    logVerbose("\n[加载参考点] " + filepath);
    
    if (!validateFile(filepath)) {
        throw FileOperationException(filepath, "文件不存在或不可读");
    }
    
    try {
        return loadReferencePointsAuto(filepath);
    } catch (const std::exception& e) {
        logError("参考点加载失败: " + std::string(e.what()));
        throw FileOperationException(filepath, e.what());
    }
}

ReferencePointsMap DataLoader::loadReferencePointsFromTSV(const std::string& filepath) {
    std::vector<std::string> lines = readAllLines(filepath);
    ReferencePointsMap ref_points;
    
    size_t line_number = 0;
    for (const auto& line : lines) {
        line_number++;
        
        if (shouldSkipLine(line)) {
            continue;
        }
        
        try {
            std::vector<std::string> tokens = parseTSVLine(line);
            
            // 对应Python: {row[3]: row[:3].astype(float) for row in data}
            if (tokens.size() < 4) {
                logVerbose("跳过第" + std::to_string(line_number) + "行: 列数不足");
                continue;
            }
            
            Point3D point;
            point.x() = stringToDouble(tokens[0]);
            point.y() = stringToDouble(tokens[1]);
            point.z() = stringToDouble(tokens[2]);
            std::string name = trim(tokens[3]);
            
            if (isValidPoint(point) && !name.empty()) {
                ref_points[name] = point;
            } else {
                logVerbose("跳过第" + std::to_string(line_number) + "行: 无效数据");
            }
            
        } catch (const std::exception& e) {
            logVerbose("跳过第" + std::to_string(line_number) + "行: " + e.what());
            continue;
        }
    }
    
    if (ref_points.empty()) {
        throw DataValidationException("未找到有效的参考点数据");
    }
    
    printReferencePointsInfo(ref_points);
    return ref_points;
}

ReferencePointsMap DataLoader::loadReferencePointsFromCSV(const std::string& filepath) {
    // CSV格式的参考点文件，假设格式为: x,y,z,name
    std::vector<std::string> lines = readAllLines(filepath);
    ReferencePointsMap ref_points;
    
    for (const auto& line : lines) {
        if (shouldSkipLine(line)) continue;
        
        std::vector<std::string> tokens = parseCSVLine(line, ',');
        if (tokens.size() >= 4) {
            try {
                Point3D point;
                point.x() = stringToDouble(tokens[0]);
                point.y() = stringToDouble(tokens[1]);
                point.z() = stringToDouble(tokens[2]);
                std::string name = trim(tokens[3]);
                
                if (isValidPoint(point) && !name.empty()) {
                    ref_points[name] = point;
                }
            } catch (const std::exception&) {
                continue;
            }
        }
    }
    
    if (ref_points.empty()) {
        throw DataValidationException("未找到有效的参考点数据");
    }
    
    printReferencePointsInfo(ref_points);
    return ref_points;
}

// ============================================================================
// 数据验证方法
// ============================================================================

bool DataLoader::validatePointCloudData(const PointCloudData& data) {
    if (data.rows() == 0 || data.cols() != 3) {
        return false;
    }
    
    return data.allFinite();
}

bool DataLoader::validateReferencePoints(const ReferencePointsMap& ref_points) {
    if (ref_points.empty()) {
        return false;
    }
    
    // 检查是否包含六点定位所需的关键点
    const auto& required_points = constants::REFERENCE_POINT_NAMES;
    for (const auto& name : required_points) {
        if (ref_points.find(name) == ref_points.end()) {
            logVerbose("缺少必需的参考点: " + name);
            return false;
        }
    }
    
    return true;
}

bool DataLoader::validateFile(const std::string& filepath) {
    return fileExists(filepath) && std::filesystem::is_regular_file(filepath);
}

// ============================================================================
// 文件信息获取方法
// ============================================================================

std::string DataLoader::getFileExtension(const std::string& filepath) {
    std::filesystem::path path(filepath);
    return path.extension().string();
}

size_t DataLoader::getFileSize(const std::string& filepath) {
    if (!fileExists(filepath)) {
        return 0;
    }
    return std::filesystem::file_size(filepath);
}

bool DataLoader::fileExists(const std::string& filepath) {
    return std::filesystem::exists(filepath);
}

DataLoader::FileInfo DataLoader::getFileInfo(const std::string& filepath) {
    FileInfo info;
    info.filepath = filepath;
    info.exists = fileExists(filepath);
    
    if (info.exists) {
        info.extension = getFileExtension(filepath);
        info.file_size = getFileSize(filepath);
        info.readable = std::filesystem::is_regular_file(filepath);
    } else {
        info.extension = "";
        info.file_size = 0;
        info.readable = false;
    }
    
    return info;
}

// ============================================================================
// 调试和日志方法
// ============================================================================

void DataLoader::printLoadInfo(const std::string& filepath, size_t point_count) {
    if (verbose_) {
        std::cout << "成功加载 " << point_count << " 个点" << std::endl;
    }
}

void DataLoader::printReferencePointsInfo(const ReferencePointsMap& ref_points) {
    if (verbose_) {
        std::cout << "成功加载 " << ref_points.size() << " 个参考点:" << std::endl;
        for (const auto& [name, point] : ref_points) {
            std::cout << "  " << name << ": [" 
                      << point.x() << ", " << point.y() << ", " << point.z() << "]" << std::endl;
        }
    }
}

// ============================================================================
// 私有辅助方法
// ============================================================================

std::vector<std::string> DataLoader::parseCSVLine(const std::string& line, char delimiter) {
    std::vector<std::string> tokens;
    std::stringstream ss(line);
    std::string token;
    
    while (std::getline(ss, token, delimiter)) {
        tokens.push_back(trim(token));
    }
    
    return tokens;
}

std::vector<std::string> DataLoader::parseTSVLine(const std::string& line) {
    return parseCSVLine(line, '\t');
}

double DataLoader::stringToDouble(const std::string& str) {
    std::string trimmed = trim(str);
    if (trimmed.empty()) {
        throw std::invalid_argument("空字符串无法转换为数字");
    }
    
    try {
        return std::stod(trimmed);
    } catch (const std::exception& e) {
        throw std::invalid_argument("无法转换为数字: " + trimmed);
    }
}

std::string DataLoader::trim(const std::string& str) {
    size_t start = str.find_first_not_of(" \t\r\n");
    if (start == std::string::npos) {
        return "";
    }
    
    size_t end = str.find_last_not_of(" \t\r\n");
    return str.substr(start, end - start + 1);
}

bool DataLoader::isNumeric(const std::string& str) {
    std::string trimmed = trim(str);
    if (trimmed.empty()) return false;
    
    try {
        std::stod(trimmed);
        return true;
    } catch (const std::exception&) {
        return false;
    }
}

bool DataLoader::isValidPoint(const Point3D& point) {
    return point.allFinite() && !point.hasNaN();
}

PointCloud DataLoader::loadPointCloudAuto(const std::string& filepath) {
    std::string ext = getFileExtension(filepath);
    std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);
    
    if (ext == ".csv") {
        return loadPointCloudFromCSV(filepath, ',');
    } else if (ext == ".txt") {
        return loadPointCloudFromTXT(filepath);
    } else if (ext == ".ply") {
        return loadPointCloudFromPLY(filepath);
    } else {
        // 默认尝试CSV格式
        return loadPointCloudFromCSV(filepath, ',');
    }
}

ReferencePointsMap DataLoader::loadReferencePointsAuto(const std::string& filepath) {
    std::string ext = getFileExtension(filepath);
    std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);
    
    if (ext == ".csv") {
        return loadReferencePointsFromCSV(filepath);
    } else {
        // 默认尝试TSV格式 (对应Python版本的制表符分隔)
        return loadReferencePointsFromTSV(filepath);
    }
}

std::vector<std::string> DataLoader::readAllLines(const std::string& filepath) {
    std::ifstream file(filepath);
    if (!file.is_open()) {
        throw FileOperationException(filepath, "无法打开文件");
    }
    
    std::vector<std::string> lines;
    std::string line;
    
    while (std::getline(file, line)) {
        lines.push_back(line);
    }
    
    return lines;
}

bool DataLoader::shouldSkipLine(const std::string& line) {
    std::string trimmed = trim(line);
    return trimmed.empty() || trimmed[0] == '#' || trimmed[0] == '%';
}

void DataLoader::logVerbose(const std::string& message) {
    if (verbose_) {
        std::cout << message << std::endl;
    }
}

void DataLoader::logError(const std::string& message) {
    std::cerr << message << std::endl;
}

// ============================================================================
// 便利函数
// ============================================================================

PointCloud loadPoints(const std::string& filepath) {
    return DataLoader::loadPointCloud(filepath);
}

ReferencePointsMap loadReferencePoints(const std::string& filepath) {
    return DataLoader::loadReferencePoints(filepath);
}

} // namespace blade_analysis
