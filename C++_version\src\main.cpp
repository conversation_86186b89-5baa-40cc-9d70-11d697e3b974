/**
 * @file main.cpp
 * @brief 叶片分析系统主程序
 * @details 对应Python版本：comparison0808.py的main函数
 */

#include "common/common_types.h"
#include "common/exceptions.h"
#include "common/point_cloud.h"
#include "common/timer.h"
#include "common/transformation_engine.h"
#include "data/data_loader.h"
#include "data/kdtree_wrapper.h"

#include <iostream>
#include <vector>

using namespace blade_analysis;

/**
 * @brief 测试核心数据结构和工具类
 */
void testCoreComponents() {
    std::cout << "\n=== 测试核心组件 ===" << std::endl;

    try {
        // 测试Timer
        Timer timer;
        timer.start("test");

        // 测试PointCloud
        PointCloud cloud = PointCloud::createTestCloud(100);
        cloud.printInfo("测试点云");

        // 测试变换
        Transform4D transform = TransformationEngine::createTranslation(Point3D(1, 2, 3));
        PointCloud transformed = cloud.transformed(transform);
        transformed.printInfo("变换后点云");

        timer.stop("test");
        timer.printTiming("test");

        std::cout << "核心组件测试通过!" << std::endl;

    } catch (const BladeAnalysisException& e) {
        std::cerr << "测试失败: " << e.what() << std::endl;
    }
}

/**
 * @brief 测试数据加载功能
 */
void testDataLoading() {
    std::cout << "\n=== 测试数据加载 ===" << std::endl;

    try {
        // 测试文件路径 (相对于构建目录)
        std::string blade_file = "data/bladedata.txt";
        std::string cad_file = "data/cad_dense.txt";
        std::string ref_file = "data/大东风二涡六点.txt";

        // 检查文件是否存在
        if (!DataLoader::fileExists(blade_file)) {
            std::cout << "警告: 叶片数据文件不存在: " << blade_file << std::endl;
            std::cout << "跳过数据加载测试" << std::endl;
            return;
        }

        Timer timer;

        // 测试点云加载
        timer.start("load_blade");
        PointCloud blade_cloud = DataLoader::loadPointCloud(blade_file);
        timer.stop("load_blade");
        blade_cloud.printInfo("叶片点云");

        // 测试KDTree
        timer.start("build_kdtree");
        KDTreeWrapper kdtree(blade_cloud);
        timer.stop("build_kdtree");

        // 测试搜索
        Point3D query_point(0, 0, 0);
        int nearest_idx = kdtree.searchNearest(query_point);
        std::cout << "最近点索引: " << nearest_idx << std::endl;

        timer.printAllTimings("数据加载测试");
        std::cout << "数据加载测试通过!" << std::endl;

    } catch (const BladeAnalysisException& e) {
        std::cerr << "数据加载测试失败: " << e.what() << std::endl;
    }
}

/**
 * @brief 主函数
 * @details 对应Python版本的main()函数结构
 */
int main() {
    try {
        std::cout << "=== 叶片分析系统 C++ 版本 ===" << std::endl;
        std::cout << "版本: 1.0.0" << std::endl;
        std::cout << "基于Python版本: comparison0808.py" << std::endl;
        
        // 测试核心组件
        testCoreComponents();

        // 测试数据加载功能
        testDataLoading();

        // TODO: 实现完整的分析流程
        // 1. 数据加载
        // 2. 六点定位粗配准
        // 3. ICP精配准
        // 4. 切片分析
        // 5. 误差计算
        // 6. 报告生成
        
        std::cout << "\n程序执行完成!" << std::endl;
        return 0;
        
    } catch (const BladeAnalysisException& e) {
        std::cerr << "\n程序执行失败: " << e.what() << std::endl;
        return 1;
    } catch (const std::exception& e) {
        std::cerr << "\n未知错误: " << e.what() << std::endl;
        return 1;
    }
}
