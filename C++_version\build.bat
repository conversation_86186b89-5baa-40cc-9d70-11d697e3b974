@echo off
REM 构建脚本 for Windows Visual Studio 2022

echo === 叶片分析系统 C++ 版本构建脚本 ===

REM 检查是否存在vcpkg
if not exist "C:\vcpkg\vcpkg.exe" (
    echo 错误: 未找到vcpkg，请先安装vcpkg到 C:\vcpkg
    echo 参考: SETUP_GUIDE.md
    pause
    exit /b 1
)

REM 创建构建目录
if not exist "build" mkdir build
cd build

echo 正在配置CMake...
cmake .. -DCMAKE_TOOLCHAIN_FILE=C:\vcpkg\scripts\buildsystems\vcpkg.cmake -DCMAKE_GENERATOR_PLATFORM=x64

if %ERRORLEVEL% neq 0 (
    echo CMake配置失败!
    echo 请检查:
    echo 1. vcpkg是否正确安装
    echo 2. Eigen3和PCL是否已安装: vcpkg list eigen3 pcl
    echo 3. Visual Studio 2022是否正确安装
    pause
    exit /b 1
)

echo 正在编译项目...
cmake --build . --config Release

if %ERRORLEVEL% neq 0 (
    echo 编译失败!
    echo 请检查编译错误信息
    pause
    exit /b 1
)

echo.
echo === 构建成功! ===
echo 可执行文件位置: build\bin\BladeAnalysis.exe
echo.

REM 复制测试数据
if not exist "data" mkdir data
copy "..\bladedata.txt" "data\" >nul 2>&1
copy "..\cad_dense.txt" "data\" >nul 2>&1
copy "..\大东风二涡六点.txt" "data\" >nul 2>&1

echo 测试数据已复制到 build\data\ 目录
echo.
echo 运行测试:
echo cd build
echo .\bin\BladeAnalysis.exe
echo.

pause
