/**
 * @file timer.cpp
 * @brief 高精度计时器类实现
 */

#include "common/timer.h"
#include "common/exceptions.h"
#include <iostream>
#include <iomanip>
#include <sstream>

namespace blade_analysis {

// ============================================================================
// Timer类实现
// ============================================================================

Timer::Timer() = default;

void Timer::start(const std::string& name) {
    validateTimerName(name);
    
    TimerInfo& info = getOrCreateTimer(name);
    info.start_time = now();
    info.state = TimerState::RUNNING;
}

double Timer::stop(const std::string& name) {
    validateTimerName(name);
    
    TimerInfo& info = getOrCreateTimer(name);
    
    if (info.state == TimerState::RUNNING) {
        info.accumulated_time += std::chrono::duration_cast<Duration>(now() - info.start_time);
    } else if (info.state == TimerState::PAUSED) {
        // 暂停状态下停止，不需要额外计算
    }
    
    info.state = TimerState::STOPPED;
    return info.accumulated_time.count();
}

void Timer::pause(const std::string& name) {
    validateTimerName(name);
    
    TimerInfo& info = getOrCreateTimer(name);
    
    if (info.state == TimerState::RUNNING) {
        info.accumulated_time += std::chrono::duration_cast<Duration>(now() - info.start_time);
        info.pause_time = now();
        info.state = TimerState::PAUSED;
    }
}

void Timer::resume(const std::string& name) {
    validateTimerName(name);
    
    TimerInfo& info = getOrCreateTimer(name);
    
    if (info.state == TimerState::PAUSED) {
        info.start_time = now();
        info.state = TimerState::RUNNING;
    }
}

void Timer::reset(const std::string& name) {
    validateTimerName(name);
    
    TimerInfo& info = getOrCreateTimer(name);
    info.accumulated_time = Duration::zero();
    info.state = TimerState::STOPPED;
}

void Timer::resetAll() {
    for (auto& [name, info] : timers_) {
        info.accumulated_time = Duration::zero();
        info.state = TimerState::STOPPED;
    }
}

double Timer::getElapsed(const std::string& name) const {
    if (!hasTimer(name)) {
        return 0.0;
    }
    
    const TimerInfo& info = getTimer(name);
    return calculateElapsed(info).count();
}

double Timer::getElapsedMs(const std::string& name) const {
    return getElapsed(name) * 1000.0;
}

double Timer::getElapsedUs(const std::string& name) const {
    return getElapsed(name) * 1000000.0;
}

std::map<std::string, double> Timer::getAllTimings() const {
    std::map<std::string, double> result;
    
    for (const auto& [name, info] : timers_) {
        result[name] = calculateElapsed(info).count();
    }
    
    return result;
}

bool Timer::hasTimer(const std::string& name) const {
    return timers_.find(name) != timers_.end();
}

bool Timer::isRunning(const std::string& name) const {
    if (!hasTimer(name)) {
        return false;
    }
    
    return getTimer(name).state == TimerState::RUNNING;
}

void Timer::printAllTimings(const std::string& title) const {
    std::cout << "\n=== " << title << " ===" << std::endl;
    
    if (timers_.empty()) {
        std::cout << "无计时记录" << std::endl;
        std::cout << std::string(40, '-') << std::endl;
        return;
    }
    
    // 计算最长名称长度用于对齐
    size_t max_name_length = 0;
    for (const auto& [name, info] : timers_) {
        max_name_length = std::max(max_name_length, name.length());
    }
    
    for (const auto& [name, info] : timers_) {
        double elapsed = calculateElapsed(info).count();
        std::cout << std::left << std::setw(max_name_length + 2) << (name + ":")
                  << std::right << std::setw(8) << std::fixed << std::setprecision(2) 
                  << elapsed << "s" << std::endl;
    }
    
    std::cout << std::string(40, '-') << std::endl;
}

void Timer::printTiming(const std::string& name) const {
    if (!hasTimer(name)) {
        std::cout << "计时器 '" << name << "' 不存在" << std::endl;
        return;
    }
    
    double elapsed = getElapsed(name);
    std::cout << name << ": " << std::fixed << std::setprecision(2) 
              << elapsed << "s" << std::endl;
}

std::string Timer::formatTime(double seconds) const {
    std::ostringstream oss;
    
    if (seconds < 1e-3) {
        oss << std::fixed << std::setprecision(1) << (seconds * 1e6) << "μs";
    } else if (seconds < 1.0) {
        oss << std::fixed << std::setprecision(1) << (seconds * 1e3) << "ms";
    } else if (seconds < 60.0) {
        oss << std::fixed << std::setprecision(2) << seconds << "s";
    } else {
        int minutes = static_cast<int>(seconds / 60);
        double remaining_seconds = seconds - minutes * 60;
        oss << minutes << "m" << std::fixed << std::setprecision(1) << remaining_seconds << "s";
    }
    
    return oss.str();
}

double Timer::getTotalTime() const {
    double total = 0.0;
    for (const auto& [name, info] : timers_) {
        total += calculateElapsed(info).count();
    }
    return total;
}

double Timer::getAverageTime() const {
    if (timers_.empty()) {
        return 0.0;
    }
    return getTotalTime() / timers_.size();
}

// ============================================================================
// AutoTimer类实现
// ============================================================================

Timer::AutoTimer::AutoTimer(Timer& timer, const std::string& name) 
    : timer_(timer), name_(name) {
    timer_.start(name_);
}

Timer::AutoTimer::~AutoTimer() {
    timer_.stop(name_);
}

Timer::AutoTimer Timer::createAutoTimer(const std::string& name) {
    return AutoTimer(*this, name);
}

// ============================================================================
// 静态方法实现
// ============================================================================

TimePoint Timer::now() {
    return std::chrono::high_resolution_clock::now();
}

double Timer::duration(const TimePoint& start, const TimePoint& end) {
    auto diff = std::chrono::duration_cast<Duration>(end - start);
    return diff.count();
}

Timer& Timer::getInstance() {
    static Timer instance;
    return instance;
}

// ============================================================================
// 私有方法实现
// ============================================================================

void Timer::validateTimerName(const std::string& name) const {
    BLADE_CHECK_DATA(!name.empty(), "计时器名称不能为空");
}

Timer::TimerInfo& Timer::getOrCreateTimer(const std::string& name) {
    return timers_[name];  // 如果不存在会自动创建
}

const Timer::TimerInfo& Timer::getTimer(const std::string& name) const {
    auto it = timers_.find(name);
    if (it == timers_.end()) {
        throw DataValidationException("计时器不存在: " + name);
    }
    return it->second;
}

Timer::Duration Timer::calculateElapsed(const TimerInfo& info) const {
    Duration elapsed = info.accumulated_time;
    
    if (info.state == TimerState::RUNNING) {
        elapsed += std::chrono::duration_cast<Duration>(now() - info.start_time);
    }
    
    return elapsed;
}

} // namespace blade_analysis
