/**
 * @file kdtree_wrapper.cpp
 * @brief KDTree搜索功能封装实现
 */

#include "data/kdtree_wrapper.h"
#include "common/timer.h"
#include <iostream>
#include <algorithm>
#include <chrono>

namespace blade_analysis {

// ============================================================================
// 构造函数和析构函数
// ============================================================================

KDTreeWrapper::KDTreeWrapper() 
    : tree_(std::make_shared<pcl::KdTreeFLANN<pcl::PointXYZ>>()),
      cloud_(std::make_shared<pcl::PointCloud<pcl::PointXYZ>>()) {
    stats_ = TreeStatistics{};
}

KDTreeWrapper::KDTreeWrapper(const PointCloud& cloud) : KDTreeWrapper() {
    buildTree(cloud);
}

KDTreeWrapper::KDTreeWrapper(const pcl::PointCloud<pcl::PointXYZ>::Ptr& pcl_cloud) : KDTreeWrapper() {
    buildTree(pcl_cloud);
}

// ============================================================================
// KDTree构建方法
// ============================================================================

void KDTreeWrapper::buildTree(const PointCloud& cloud) {
    if (cloud.empty()) {
        throw PointCloudException("KDTree构建", "输入点云为空");
    }
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // 转换为PCL格式
    cloud_ = toPCLCloud(cloud);
    
    // 构建KDTree
    tree_->setInputCloud(cloud_);
    
    auto end_time = std::chrono::high_resolution_clock::now();
    stats_.build_time = std::chrono::duration<double>(end_time - start_time).count();
    stats_.total_points = cloud.size();
    stats_.total_searches = 0;
    stats_.average_search_time = 0.0;
    
    // 可以通过DataLoader的verbose设置来控制输出
    std::cout << "KDTree构建完成，点数: " << stats_.total_points
              << "，耗时: " << stats_.build_time << "s" << std::endl;
}

void KDTreeWrapper::buildTree(const pcl::PointCloud<pcl::PointXYZ>::Ptr& pcl_cloud) {
    if (!pcl_cloud || pcl_cloud->empty()) {
        throw PointCloudException("KDTree构建", "输入PCL点云为空");
    }
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    cloud_ = pcl_cloud;
    tree_->setInputCloud(cloud_);
    
    auto end_time = std::chrono::high_resolution_clock::now();
    stats_.build_time = std::chrono::duration<double>(end_time - start_time).count();
    stats_.total_points = pcl_cloud->size();
    stats_.total_searches = 0;
    stats_.average_search_time = 0.0;
}

size_t KDTreeWrapper::size() const {
    return cloud_ ? cloud_->size() : 0;
}

void KDTreeWrapper::clear() {
    tree_.reset();
    cloud_.reset();
    tree_ = std::make_shared<pcl::KdTreeFLANN<pcl::PointXYZ>>();
    cloud_ = std::make_shared<pcl::PointCloud<pcl::PointXYZ>>();
    stats_ = TreeStatistics{};
}

// ============================================================================
// 搜索方法
// ============================================================================

SearchResult KDTreeWrapper::searchKNN(const Point3D& query_point, int k) const {
    validateTree();
    validateQueryPoint(query_point);
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    pcl::PointXYZ pcl_point = toPCLPoint(query_point);
    std::vector<int> indices(k);
    std::vector<float> distances(k);
    
    int found = tree_->nearestKSearch(pcl_point, k, indices, distances);
    
    auto end_time = std::chrono::high_resolution_clock::now();
    double search_time = std::chrono::duration<double>(end_time - start_time).count();
    recordSearchTime(search_time);
    
    // 调整结果大小
    indices.resize(found);
    distances.resize(found);
    
    return convertPCLResult(indices, distances);
}

int KDTreeWrapper::searchNearest(const Point3D& query_point) const {
    SearchResult result = searchKNN(query_point, 1);
    return result.empty() ? -1 : result.indices[0];
}

std::pair<int, double> KDTreeWrapper::searchNearestWithDistance(const Point3D& query_point) const {
    SearchResult result = searchKNN(query_point, 1);
    if (result.empty()) {
        return std::make_pair(-1, -1.0);
    }
    return std::make_pair(result.indices[0], result.distances[0]);
}

SearchResult KDTreeWrapper::searchRadius(const Point3D& query_point, double radius) const {
    validateTree();
    validateQueryPoint(query_point);
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    pcl::PointXYZ pcl_point = toPCLPoint(query_point);
    std::vector<int> indices;
    std::vector<float> distances;
    
    int found = tree_->radiusSearch(pcl_point, radius, indices, distances);
    
    auto end_time = std::chrono::high_resolution_clock::now();
    double search_time = std::chrono::duration<double>(end_time - start_time).count();
    recordSearchTime(search_time);
    
    return convertPCLResult(indices, distances);
}

std::vector<int> KDTreeWrapper::searchNearestBatch(const std::vector<Point3D>& query_points) const {
    std::vector<int> results;
    results.reserve(query_points.size());
    
    for (const auto& point : query_points) {
        results.push_back(searchNearest(point));
    }
    
    return results;
}

std::vector<double> KDTreeWrapper::computeDistances(const std::vector<Point3D>& query_points) const {
    std::vector<double> distances;
    distances.reserve(query_points.size());
    
    for (const auto& point : query_points) {
        auto [index, distance] = searchNearestWithDistance(point);
        distances.push_back(distance);
    }
    
    return distances;
}

std::vector<double> KDTreeWrapper::computeDistances(const PointCloud& query_cloud) const {
    std::vector<Point3D> query_points;
    query_points.reserve(query_cloud.size());
    
    for (size_t i = 0; i < query_cloud.size(); ++i) {
        query_points.push_back(query_cloud.getPoint(i));
    }
    
    return computeDistances(query_points);
}

double KDTreeWrapper::computeDistance(const Point3D& query_point) const {
    auto [index, distance] = searchNearestWithDistance(query_point);
    return distance;
}

// ============================================================================
// PCL集成方法
// ============================================================================

pcl::PointCloud<pcl::PointXYZ>::Ptr KDTreeWrapper::toPCLCloud(const PointCloud& cloud) {
    auto pcl_cloud = std::make_shared<pcl::PointCloud<pcl::PointXYZ>>();
    pcl_cloud->points.reserve(cloud.size());
    
    for (size_t i = 0; i < cloud.size(); ++i) {
        Point3D point = cloud.getPoint(i);
        pcl_cloud->points.push_back(toPCLPoint(point));
    }
    
    pcl_cloud->width = cloud.size();
    pcl_cloud->height = 1;
    pcl_cloud->is_dense = true;
    
    return pcl_cloud;
}

PointCloud KDTreeWrapper::fromPCLCloud(const pcl::PointCloud<pcl::PointXYZ>::Ptr& pcl_cloud) {
    if (!pcl_cloud || pcl_cloud->empty()) {
        return PointCloud();
    }
    
    std::vector<Point3D> points;
    points.reserve(pcl_cloud->size());
    
    for (const auto& pcl_point : pcl_cloud->points) {
        points.push_back(fromPCLPoint(pcl_point));
    }
    
    return PointCloud(points);
}

// ============================================================================
// 私有辅助方法
// ============================================================================

void KDTreeWrapper::validateTree() const {
    if (!isBuilt()) {
        throw PointCloudException("KDTree搜索", "KDTree未构建");
    }
}

void KDTreeWrapper::validateQueryPoint(const Point3D& point) const {
    if (!point.allFinite() || point.hasNaN()) {
        throw DataValidationException("查询点包含无效数值");
    }
}

void KDTreeWrapper::recordSearchTime(double time) const {
    stats_.total_searches++;
    stats_.average_search_time = (stats_.average_search_time * (stats_.total_searches - 1) + time) / stats_.total_searches;
}

SearchResult KDTreeWrapper::convertPCLResult(const std::vector<int>& indices, 
                                              const std::vector<float>& distances) const {
    std::vector<double> double_distances;
    double_distances.reserve(distances.size());
    
    for (float dist : distances) {
        double_distances.push_back(static_cast<double>(dist));
    }
    
    return SearchResult(indices, double_distances);
}

pcl::PointXYZ KDTreeWrapper::toPCLPoint(const Point3D& point) {
    return pcl::PointXYZ(static_cast<float>(point.x()), 
                         static_cast<float>(point.y()), 
                         static_cast<float>(point.z()));
}

Point3D KDTreeWrapper::fromPCLPoint(const pcl::PointXYZ& pcl_point) {
    return Point3D(static_cast<double>(pcl_point.x),
                   static_cast<double>(pcl_point.y),
                   static_cast<double>(pcl_point.z));
}

// ============================================================================
// 便利函数
// ============================================================================

std::vector<int> quickSearchNearest(const PointCloud& target_cloud, 
                                     const std::vector<Point3D>& query_points) {
    KDTreeWrapper kdtree(target_cloud);
    return kdtree.searchNearestBatch(query_points);
}

std::vector<double> quickComputeDistances(const PointCloud& target_cloud, 
                                          const PointCloud& query_cloud) {
    KDTreeWrapper kdtree(target_cloud);
    return kdtree.computeDistances(query_cloud);
}

} // namespace blade_analysis
