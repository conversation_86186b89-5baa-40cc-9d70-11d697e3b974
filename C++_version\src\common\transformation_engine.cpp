/**
 * @file transformation_engine.cpp
 * @brief 变换矩阵处理引擎实现
 */

#include "common/transformation_engine.h"
#include "common/point_cloud.h"
#include <iostream>
#include <iomanip>

namespace blade_analysis {

// ============================================================================
// 静态变换应用方法
// ============================================================================

PointCloud TransformationEngine::applyTransform(const PointCloud& points, const Transform4D& transform) {
    validateTransform(transform);
    
    PointCloudData transformed_points = applyTransform(points.getPoints(), transform);
    return PointCloud(transformed_points);
}

Point3D TransformationEngine::applyTransform(const Point3D& point, const Transform4D& transform) {
    validateTransform(transform);
    
    Eigen::Vector4d homogeneous = toHomogeneous(point);
    Eigen::Vector4d transformed = transform * homogeneous;
    return fromHomogeneous(transformed);
}

std::vector<Point3D> TransformationEngine::applyTransform(const std::vector<Point3D>& points, const Transform4D& transform) {
    validateTransform(transform);
    
    std::vector<Point3D> result;
    result.reserve(points.size());
    
    for (const auto& point : points) {
        result.push_back(applyTransform(point, transform));
    }
    
    return result;
}

PointCloudData TransformationEngine::applyTransform(const PointCloudData& points, const Transform4D& transform) {
    validateTransform(transform);
    BLADE_CHECK_DATA(points.cols() == 3, "点云数据必须是N×3矩阵");
    
    if (points.rows() == 0) {
        return points;
    }
    
    // 转换为齐次坐标 (对应Python: np.column_stack([points[:, :3], np.ones(len(points))]))
    Eigen::MatrixXd homogeneous = toHomogeneous(points);
    
    // 应用变换 (对应Python: transform @ homogeneous.T)
    Eigen::MatrixXd transformed = (transform * homogeneous.transpose()).transpose();
    
    // 转换回3D坐标 (对应Python: result[:, :3])
    return fromHomogeneous(transformed);
}

// ============================================================================
// 变换矩阵创建方法
// ============================================================================

Transform4D TransformationEngine::createIdentity() {
    return Transform4D::Identity();
}

Transform4D TransformationEngine::createTransform(const Rotation3D& rotation, const Point3D& translation) {
    validateRotation(rotation);
    
    Transform4D transform = Transform4D::Identity();
    transform.block<3, 3>(0, 0) = rotation;
    transform.block<3, 1>(0, 3) = translation;
    return transform;
}

Transform4D TransformationEngine::createTransform(double roll, double pitch, double yaw, const Point3D& translation) {
    // 创建欧拉角旋转矩阵 (ZYX顺序)
    Eigen::AngleAxisd roll_angle(roll, Eigen::Vector3d::UnitX());
    Eigen::AngleAxisd pitch_angle(pitch, Eigen::Vector3d::UnitY());
    Eigen::AngleAxisd yaw_angle(yaw, Eigen::Vector3d::UnitZ());
    
    Rotation3D rotation = yaw_angle * pitch_angle * roll_angle;
    return createTransform(rotation, translation);
}

Transform4D TransformationEngine::createTranslation(const Point3D& translation) {
    Transform4D transform = Transform4D::Identity();
    transform.block<3, 1>(0, 3) = translation;
    return transform;
}

Transform4D TransformationEngine::createRotation(const Rotation3D& rotation) {
    validateRotation(rotation);
    return createTransform(rotation, Point3D::Zero());
}

Transform4D TransformationEngine::createRotation(const Point3D& axis, double angle) {
    Point3D normalized_axis = axis.normalized();
    Eigen::AngleAxisd angle_axis(angle, normalized_axis);
    Rotation3D rotation = angle_axis.toRotationMatrix();
    return createRotation(rotation);
}

// ============================================================================
// 变换矩阵分解方法
// ============================================================================

Rotation3D TransformationEngine::extractRotation(const Transform4D& transform) {
    validateTransform(transform);
    return transform.block<3, 3>(0, 0);
}

Point3D TransformationEngine::extractTranslation(const Transform4D& transform) {
    validateTransform(transform);
    return transform.block<3, 1>(0, 3);
}

std::pair<Rotation3D, Point3D> TransformationEngine::decomposeTransform(const Transform4D& transform) {
    return std::make_pair(extractRotation(transform), extractTranslation(transform));
}

std::tuple<double, double, double> TransformationEngine::toEulerAngles(const Rotation3D& rotation) {
    validateRotation(rotation);
    
    // 使用ZYX欧拉角约定
    Eigen::Vector3d euler = rotation.eulerAngles(2, 1, 0);  // ZYX顺序
    return std::make_tuple(euler(2), euler(1), euler(0));   // 返回roll, pitch, yaw
}

// ============================================================================
// 变换矩阵运算方法
// ============================================================================

Transform4D TransformationEngine::inverse(const Transform4D& transform) {
    validateTransform(transform);
    
    // 对于刚体变换，可以使用更高效的逆变换公式
    if (isRigidTransform(transform)) {
        Transform4D inv = Transform4D::Identity();
        Rotation3D R = extractRotation(transform);
        Point3D t = extractTranslation(transform);
        
        inv.block<3, 3>(0, 0) = R.transpose();
        inv.block<3, 1>(0, 3) = -R.transpose() * t;
        return inv;
    } else {
        return transform.inverse();
    }
}

Transform4D TransformationEngine::compose(const Transform4D& transform1, const Transform4D& transform2) {
    validateTransform(transform1);
    validateTransform(transform2);
    return transform2 * transform1;
}

Transform4D TransformationEngine::interpolate(const Transform4D& transform1, const Transform4D& transform2, double t) {
    validateTransform(transform1);
    validateTransform(transform2);
    BLADE_CHECK_DATA(t >= 0.0 && t <= 1.0, "插值参数t必须在[0,1]范围内");
    
    // 分解变换矩阵
    auto [R1, t1] = decomposeTransform(transform1);
    auto [R2, t2] = decomposeTransform(transform2);
    
    // 插值平移
    Point3D interpolated_translation = (1.0 - t) * t1 + t * t2;
    
    // 插值旋转 (使用四元数球面线性插值)
    Eigen::Quaterniond q1(R1);
    Eigen::Quaterniond q2(R2);
    Eigen::Quaterniond interpolated_q = q1.slerp(t, q2);
    Rotation3D interpolated_rotation = interpolated_q.toRotationMatrix();
    
    return createTransform(interpolated_rotation, interpolated_translation);
}

Transform4D TransformationEngine::computeRelativeTransform(const Transform4D& from, const Transform4D& to) {
    return compose(inverse(from), to);
}

// ============================================================================
// 变换矩阵验证方法
// ============================================================================

bool TransformationEngine::isValidTransform(const Transform4D& transform) {
    // 检查矩阵大小
    if (transform.rows() != 4 || transform.cols() != 4) {
        return false;
    }
    
    // 检查数值有效性
    if (!transform.allFinite()) {
        return false;
    }
    
    // 检查最后一行是否为[0, 0, 0, 1]
    Eigen::Vector4d last_row = transform.row(3);
    Eigen::Vector4d expected_last_row(0, 0, 0, 1);
    if (!last_row.isApprox(expected_last_row, constants::NUMERICAL_TOLERANCE)) {
        return false;
    }
    
    return true;
}

bool TransformationEngine::isValidRotation(const Rotation3D& rotation) {
    // 检查矩阵大小
    if (rotation.rows() != 3 || rotation.cols() != 3) {
        return false;
    }
    
    // 检查数值有效性
    if (!rotation.allFinite()) {
        return false;
    }
    
    // 检查是否为正交矩阵
    Rotation3D should_be_identity = rotation * rotation.transpose();
    if (!should_be_identity.isApprox(Rotation3D::Identity(), constants::NUMERICAL_TOLERANCE)) {
        return false;
    }
    
    // 检查行列式是否为1 (右手坐标系)
    double det = rotation.determinant();
    if (std::abs(det - 1.0) > constants::NUMERICAL_TOLERANCE) {
        return false;
    }
    
    return true;
}

bool TransformationEngine::isIdentity(const Transform4D& transform, double tolerance) {
    return transform.isApprox(Transform4D::Identity(), tolerance);
}

bool TransformationEngine::isRigidTransform(const Transform4D& transform, double tolerance) {
    if (!isValidTransform(transform)) {
        return false;
    }
    
    Rotation3D rotation = extractRotation(transform);
    return isValidRotation(rotation);
}

// ============================================================================
// 坐标系变换方法
// ============================================================================

Eigen::MatrixXd TransformationEngine::toHomogeneous(const PointCloudData& points) {
    BLADE_CHECK_DATA(points.cols() == 3, "点云数据必须是N×3矩阵");
    
    Eigen::MatrixXd homogeneous(points.rows(), 4);
    homogeneous.leftCols<3>() = points;
    homogeneous.rightCols<1>().setOnes();
    return homogeneous;
}

PointCloudData TransformationEngine::fromHomogeneous(const Eigen::MatrixXd& homogeneous) {
    BLADE_CHECK_DATA(homogeneous.cols() == 4, "齐次坐标必须是N×4矩阵");
    
    PointCloudData points(homogeneous.rows(), 3);
    for (int i = 0; i < homogeneous.rows(); ++i) {
        double w = homogeneous(i, 3);
        BLADE_CHECK_DATA(std::abs(w) > constants::NUMERICAL_TOLERANCE, "齐次坐标的w分量不能为0");
        points.row(i) = homogeneous.row(i).head<3>() / w;
    }
    return points;
}

Eigen::Vector4d TransformationEngine::toHomogeneous(const Point3D& point) {
    Eigen::Vector4d homogeneous;
    homogeneous.head<3>() = point;
    homogeneous(3) = 1.0;
    return homogeneous;
}

Point3D TransformationEngine::fromHomogeneous(const Eigen::Vector4d& homogeneous) {
    double w = homogeneous(3);
    BLADE_CHECK_DATA(std::abs(w) > constants::NUMERICAL_TOLERANCE, "齐次坐标的w分量不能为0");
    return homogeneous.head<3>() / w;
}

// ============================================================================
// 调试和输出方法
// ============================================================================

void TransformationEngine::printTransform(const Transform4D& transform, const std::string& title) {
    std::cout << "\n=== " << title << " ===" << std::endl;
    std::cout << std::fixed << std::setprecision(6);
    std::cout << transform << std::endl;
    std::cout << std::string(40, '-') << std::endl;
}

std::string TransformationEngine::formatTransform(const Transform4D& transform) {
    std::ostringstream oss;
    oss << std::fixed << std::setprecision(6);
    oss << transform;
    return oss.str();
}

// ============================================================================
// 私有辅助方法
// ============================================================================

void TransformationEngine::validateTransform(const Transform4D& transform) {
    BLADE_CHECK_MATRIX(isValidTransform(transform), "变换矩阵验证", "无效的4×4变换矩阵");
}

void TransformationEngine::validateRotation(const Rotation3D& rotation) {
    BLADE_CHECK_MATRIX(isValidRotation(rotation), "旋转矩阵验证", "无效的3×3旋转矩阵");
}

// ============================================================================
// 全局运算符重载
// ============================================================================

Transform4D operator*(const Transform4D& lhs, const Transform4D& rhs) {
    return TransformationEngine::compose(rhs, lhs);  // 注意顺序
}

Point3D operator*(const Transform4D& transform, const Point3D& point) {
    return TransformationEngine::applyTransform(point, transform);
}

std::ostream& operator<<(std::ostream& os, const Transform4D& transform) {
    os << transform;
    return os;
}

} // namespace blade_analysis
