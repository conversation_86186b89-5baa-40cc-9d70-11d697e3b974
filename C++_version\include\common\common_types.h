/**
 * @file common_types.h
 * @brief 通用数据类型定义
 * @details 定义项目中使用的通用数据类型、常量和类型别名
 * 
 * 对应Python版本：comparison0808.py中的numpy数组和字典类型
 */

#pragma once

#include <Eigen/Dense>
#include <vector>
#include <map>
#include <string>
#include <memory>
#include <chrono>

namespace blade_analysis {

// ============================================================================
// 基础数据类型别名
// ============================================================================

/// 3D点类型 (对应Python: np.array([x, y, z]))
using Point3D = Eigen::Vector3d;

/// 2D点类型 (对应Python: np.array([x, z]) 切片数据)
using Point2D = Eigen::Vector2d;

/// 4x4变换矩阵类型 (对应Python: np.eye(4))
using Transform4D = Eigen::Matrix4d;

/// 3x3旋转矩阵类型
using Rotation3D = Eigen::Matrix3d;

/// 动态矩阵类型 (对应Python: np.ndarray)
using MatrixXd = Eigen::MatrixXd;

/// 动态向量类型
using VectorXd = Eigen::VectorXd;

/// 点云数据类型 (对应Python: np.array([[x1,y1,z1], [x2,y2,z2], ...]))
using PointCloudData = Eigen::MatrixXd;

/// 参考点映射类型 (对应Python: dict{str: np.array([x,y,z])})
using ReferencePointsMap = std::map<std::string, Point3D>;

/// 距离数组类型 (对应Python: np.array([d1, d2, d3, ...]))
using DistanceArray = std::vector<double>;

/// 时间点类型
using TimePoint = std::chrono::high_resolution_clock::time_point;

/// 时间间隔类型
using Duration = std::chrono::duration<double>;

// ============================================================================
// 切片数据结构
// ============================================================================

/**
 * @brief 2D切片数据结构
 * @details 对应Python版本中的切片元组 (y_value, slice_xz)
 */
struct SliceData2D {
    double y_value;           ///< Y轴坐标值
    Eigen::MatrixXd points;   ///< 2D点云数据 (N×2矩阵，列为[X, Z])
    
    SliceData2D() : y_value(0.0) {}
    SliceData2D(double y, const Eigen::MatrixXd& pts) : y_value(y), points(pts) {}
    
    /// 获取点数
    size_t size() const { return static_cast<size_t>(points.rows()); }
    
    /// 检查是否为空
    bool empty() const { return points.rows() == 0; }
};

/**
 * @brief 误差统计数据结构
 * @details 对应Python版本中的误差字典
 */
struct ErrorStatistics {
    double mean_error;                    ///< 平均误差 (对应Python: 'mean_error')
    double max_error;                     ///< 最大误差 (对应Python: 'max_error')
    double std_error;                     ///< 标准差 (对应Python: 'std_error')
    DistanceArray error_distribution;     ///< 误差分布 (对应Python: 'error_distribution')
    
    ErrorStatistics() : mean_error(0.0), max_error(0.0), std_error(0.0) {}
    
    /// 计算统计信息
    void computeStatistics(const DistanceArray& distances);
    
    /// 获取百分位数
    double getPercentile(double percentile) const;
    
    /// 检查是否有效
    bool isValid() const { return !error_distribution.empty(); }
};

/**
 * @brief 切片分析结果
 * @details 对应Python版本中的results列表元素
 */
struct SliceAnalysisResult {
    double y_value;                    ///< Y轴坐标
    SliceData2D blade_slice;          ///< 叶片切片数据
    SliceData2D cad_slice;            ///< CAD切片数据
    ErrorStatistics errors;           ///< 误差统计
    
    SliceAnalysisResult() : y_value(0.0) {}
    SliceAnalysisResult(double y) : y_value(y) {}
};

// ============================================================================
// 文件信息结构
// ============================================================================

/**
 * @brief 文件信息结构
 * @details 对应Python版本中的file_info字典
 */
struct FileInfo {
    size_t blade_count;    ///< 叶片点数 (对应Python: 'blade_count')
    size_t cad_count;      ///< CAD点数 (对应Python: 'cad_count')
    
    FileInfo() : blade_count(0), cad_count(0) {}
    FileInfo(size_t blade, size_t cad) : blade_count(blade), cad_count(cad) {}
};

// ============================================================================
// 配置常量
// ============================================================================

namespace constants {
    /// 默认切片厚度 (mm) - 对应Python: thickness=1.0
    constexpr double DEFAULT_SLICE_THICKNESS = 1.0;
    
    /// ICP最大迭代次数 - 对应Python: max_iteration=2000
    constexpr int ICP_MAX_ITERATIONS = 2000;
    
    /// 体素降采样分割数 - 对应Python: / 50
    constexpr int VOXEL_DIVISION_FACTOR = 50;
    
    /// 误差过滤百分位数 - 对应Python: np.percentile(errors, 99)
    constexpr double ERROR_FILTER_PERCENTILE = 99.0;
    
    /// 六点定位参考点名称 - 对应Python: ['A1', 'A2', 'A3', 'B4', 'B5', 'C6']
    const std::vector<std::string> REFERENCE_POINT_NAMES = {
        "A1", "A2", "A3", "B4", "B5", "C6"
    };
    
    /// 数值精度容差
    constexpr double NUMERICAL_TOLERANCE = 1e-10;
    
    /// 最小点数阈值
    constexpr size_t MIN_POINTS_THRESHOLD = 10;
}

// ============================================================================
// 智能指针类型别名
// ============================================================================

/// 点云智能指针
template<typename T>
using UniquePtr = std::unique_ptr<T>;

template<typename T>
using SharedPtr = std::shared_ptr<T>;

} // namespace blade_analysis
