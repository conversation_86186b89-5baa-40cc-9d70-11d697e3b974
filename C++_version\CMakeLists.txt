cmake_minimum_required(VERSION 3.16)
project(BladeAnalysis VERSION 1.0.0 LANGUAGES CXX)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置编译选项
if(MSVC)
    add_compile_options(/W4 /utf-8)
    # 设置运行时库为多线程DLL
    set(CMAKE_MSVC_RUNTIME_LIBRARY "MultiThreadedDLL")
else()
    add_compile_options(-Wall -Wextra -pedantic)
endif()

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# 查找依赖库
find_package(Eigen3 REQUIRED)
find_package(PCL REQUIRED COMPONENTS 
    common 
    io 
    kdtree 
    search 
    registration 
    filters
)

# 包含目录
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)
include_directories(${PCL_INCLUDE_DIRS})

# 添加PCL定义
add_definitions(${PCL_DEFINITIONS})

# 源文件列表
set(CORE_SOURCES
    src/common/point_cloud.cpp
    src/common/timer.cpp
    src/common/exceptions.cpp
    src/common/transformation_engine.cpp
    src/common/error_statistics.cpp
)

set(DATA_SOURCES
    src/data/data_loader.cpp
    src/data/kdtree_wrapper.cpp
)

set(ALGORITHM_SOURCES
    src/algorithms/coarse_alignment.cpp
    src/algorithms/icp_refinement.cpp
    src/algorithms/slice_analyzer.cpp
    src/algorithms/error_statistics.cpp
)

set(VISUALIZATION_SOURCES
    src/visualization/visualization_engine.cpp
    src/visualization/report_generator.cpp
)

set(ALL_SOURCES
    ${CORE_SOURCES}
    ${DATA_SOURCES}
    ${ALGORITHM_SOURCES}
    ${VISUALIZATION_SOURCES}
    src/main.cpp
)

# 头文件列表
set(HEADERS
    include/common/common_types.h
    include/common/exceptions.h
    include/common/point_cloud.h
    include/common/timer.h
    include/common/transformation_engine.h
    include/data/data_loader.h
    include/data/kdtree_wrapper.h
    include/algorithms/coarse_alignment.h
    include/algorithms/icp_refinement.h
    include/algorithms/slice_analyzer.h
    include/algorithms/error_statistics.h
    include/visualization/visualization_engine.h
    include/visualization/report_generator.h
)

# 创建可执行文件
add_executable(${PROJECT_NAME} ${ALL_SOURCES} ${HEADERS})

# 链接库
target_link_libraries(${PROJECT_NAME}
    Eigen3::Eigen
    ${PCL_LIBRARIES}
)

# 设置目标属性
set_target_properties(${PROJECT_NAME} PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
    DEBUG_POSTFIX "_d"
)

# 安装规则
install(TARGETS ${PROJECT_NAME}
    RUNTIME DESTINATION bin
)

# 复制测试数据
file(COPY ${CMAKE_CURRENT_SOURCE_DIR}/../bladedata.txt 
     DESTINATION ${CMAKE_BINARY_DIR}/data/)
file(COPY ${CMAKE_CURRENT_SOURCE_DIR}/../cad_dense.txt 
     DESTINATION ${CMAKE_BINARY_DIR}/data/)
file(COPY ${CMAKE_CURRENT_SOURCE_DIR}/../大东风二涡六点.txt 
     DESTINATION ${CMAKE_BINARY_DIR}/data/)

# 打印配置信息
message(STATUS "=== BladeAnalysis Configuration ===")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "Eigen3 version: ${Eigen3_VERSION}")
message(STATUS "PCL version: ${PCL_VERSION}")
message(STATUS "Install prefix: ${CMAKE_INSTALL_PREFIX}")