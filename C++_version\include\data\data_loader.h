/**
 * @file data_loader.h
 * @brief 数据加载器类定义
 * @details 实现点云数据和参考点的文件加载功能
 * 
 * 对应Python版本：comparison0808.py中的load_points和load_reference_points函数
 */

#pragma once

#include "common/common_types.h"
#include "common/exceptions.h"
#include "common/point_cloud.h"
#include <string>
#include <filesystem>

namespace blade_analysis {

/**
 * @brief 数据加载器类
 * @details 提供各种格式文件的加载功能，对应Python版本中的数据加载函数
 */
class DataLoader {
public:
    // ========================================================================
    // 点云数据加载方法
    // ========================================================================
    
    /// 加载点云数据 (对应Python: load_points)
    static PointCloud loadPointCloud(const std::string& filepath);
    
    /// 从CSV文件加载点云数据
    static PointCloud loadPointCloudFromCSV(const std::string& filepath, char delimiter = ',');
    
    /// 从TXT文件加载点云数据
    static PointCloud loadPointCloudFromTXT(const std::string& filepath);
    
    /// 从PLY文件加载点云数据
    static PointCloud loadPointCloudFromPLY(const std::string& filepath);

    // ========================================================================
    // 参考点加载方法
    // ========================================================================
    
    /// 加载六点定位参考点 (对应Python: load_reference_points)
    static ReferencePointsMap loadReferencePoints(const std::string& filepath);
    
    /// 从TSV文件加载参考点
    static ReferencePointsMap loadReferencePointsFromTSV(const std::string& filepath);
    
    /// 从CSV文件加载参考点
    static ReferencePointsMap loadReferencePointsFromCSV(const std::string& filepath);

    // ========================================================================
    // 数据验证方法
    // ========================================================================
    
    /// 验证点云数据有效性
    static bool validatePointCloudData(const PointCloudData& data);
    
    /// 验证参考点数据有效性
    static bool validateReferencePoints(const ReferencePointsMap& ref_points);
    
    /// 验证文件存在性和可读性
    static bool validateFile(const std::string& filepath);

    // ========================================================================
    // 文件信息获取方法
    // ========================================================================
    
    /// 获取文件扩展名
    static std::string getFileExtension(const std::string& filepath);
    
    /// 获取文件大小
    static size_t getFileSize(const std::string& filepath);
    
    /// 检查文件是否存在
    static bool fileExists(const std::string& filepath);
    
    /// 获取文件信息
    struct FileInfo {
        std::string filepath;
        std::string extension;
        size_t file_size;
        bool exists;
        bool readable;
    };
    static FileInfo getFileInfo(const std::string& filepath);

    // ========================================================================
    // 数据预处理方法
    // ========================================================================
    
    /// 移除重复点
    static PointCloud removeDuplicatePoints(const PointCloud& cloud, double tolerance = 1e-6);
    
    /// 移除异常值点
    static PointCloud removeOutliers(const PointCloud& cloud, double std_multiplier = 2.0);
    
    /// 数据统计信息
    struct DataStatistics {
        size_t total_points;
        size_t valid_points;
        size_t duplicate_points;
        size_t outlier_points;
        Point3D min_point;
        Point3D max_point;
        Point3D centroid;
        double max_extent;
    };
    static DataStatistics analyzePointCloud(const PointCloud& cloud);

    // ========================================================================
    // 调试和日志方法
    // ========================================================================
    
    /// 打印加载信息 (对应Python: print语句)
    static void printLoadInfo(const std::string& filepath, size_t point_count);
    
    /// 打印参考点信息
    static void printReferencePointsInfo(const ReferencePointsMap& ref_points);
    
    /// 设置详细输出模式
    static void setVerbose(bool verbose) { verbose_ = verbose; }
    
    /// 获取详细输出模式状态
    static bool isVerbose() { return verbose_; }

private:
    // ========================================================================
    // 私有成员变量
    // ========================================================================
    
    /// 详细输出模式标志
    static bool verbose_;

    // ========================================================================
    // 私有辅助方法
    // ========================================================================
    
    /// 解析CSV行
    static std::vector<std::string> parseCSVLine(const std::string& line, char delimiter);
    
    /// 解析TSV行
    static std::vector<std::string> parseTSVLine(const std::string& line);
    
    /// 字符串转换为double
    static double stringToDouble(const std::string& str);
    
    /// 去除字符串首尾空白
    static std::string trim(const std::string& str);
    
    /// 检查字符串是否为数字
    static bool isNumeric(const std::string& str);
    
    /// 验证点坐标有效性
    static bool isValidPoint(const Point3D& point);
    
    /// 从文件路径推断加载方法
    static PointCloud loadPointCloudAuto(const std::string& filepath);
    
    /// 从文件路径推断参考点加载方法
    static ReferencePointsMap loadReferencePointsAuto(const std::string& filepath);
    
    /// 读取文件所有行
    static std::vector<std::string> readAllLines(const std::string& filepath);
    
    /// 跳过注释行和空行
    static bool shouldSkipLine(const std::string& line);
    
    /// 记录详细日志
    static void logVerbose(const std::string& message);
    
    /// 记录错误日志
    static void logError(const std::string& message);
};

// ============================================================================
// 便利函数
// ============================================================================

/// 快速加载点云数据
PointCloud loadPoints(const std::string& filepath);

/// 快速加载参考点数据
ReferencePointsMap loadReferencePoints(const std::string& filepath);

} // namespace blade_analysis
